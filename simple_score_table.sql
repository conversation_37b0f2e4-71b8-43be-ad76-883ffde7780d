-- 创建简单的学生成绩表，不使用外键约束

-- 删除可能存在的外键约束
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS sys_student_score;

-- 创建学生成绩表（无外键约束）
CREATE TABLE sys_student_score (
  score_id          bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '成绩ID',
  student_id        bigint(20)      NOT NULL                   COMMENT '学生ID',
  item_id           bigint(20)      DEFAULT NULL               COMMENT '考核项目ID',
  criterion_id      bigint(20)      DEFAULT NULL               COMMENT '使用的标准ID',
  raw_score         decimal(10,2)   DEFAULT NULL               COMMENT '原始成绩',
  grade_score       varchar(20)     DEFAULT ''                 COMMENT '等级成绩(excellent,good,pass,fail)',
  score_points      decimal(5,2)    DEFAULT NULL               COMMENT '分数(百分制)',
  exam_date         date            DEFAULT NULL               COMMENT '考试日期',
  exam_semester     varchar(20)     DEFAULT ''                 COMMENT '考试学期',
  exam_year         bigint(20)      DEFAULT NULL               COMMENT '考试年份',
  import_batch      varchar(50)     DEFAULT ''                 COMMENT '导入批次号',
  is_manual         char(1)         DEFAULT '0'                COMMENT '是否人工录入(0自动导入1人工录入)',
  status            char(1)         DEFAULT '1'                COMMENT '状态(0无效1有效)',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (score_id),
  KEY idx_student_id (student_id),
  KEY idx_item_id (item_id),
  KEY idx_exam_date (exam_date),
  KEY idx_exam_semester (exam_semester),
  KEY idx_status (status)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '学生成绩表';

-- 插入测试数据
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(1, 1, 22.5, 'good', 85.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(1, 2, 3200.0, 'excellent', 95.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(1, 3, 8.5, 'good', 88.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试数据'),
(2, 1, 20.8, 'pass', 75.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(2, 2, 2800.0, 'good', 82.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(3, 4, 220.0, 'excellent', 92.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试数据'),
(3, 5, 15.5, 'good', 86.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试数据'),
(4, 6, 45, 'excellent', 94.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试数据'),
(4, 7, 12, 'good', 87.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试数据'),
(5, 8, 240.5, 'pass', 78.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试数据'),
(6, 9, 300.2, 'pass', 76.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑测试数据'),
(7, 1, 24.1, 'excellent', 92.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(7, 2, 3500.0, 'excellent', 98.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(8, 3, 7.8, 'excellent', 95.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试数据'),
(8, 4, 250.0, 'excellent', 96.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试数据'),
(9, 5, 18.2, 'excellent', 93.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试数据'),
(9, 6, 50, 'excellent', 97.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试数据'),
(10, 7, 15, 'excellent', 98.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试数据'),
(10, 8, 220.8, 'excellent', 94.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试数据');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据
SELECT 
    score_id, student_id, item_id, raw_score, grade_score, score_points,
    CASE item_id 
        WHEN 1 THEN '身高体重'
        WHEN 2 THEN '肺活量'
        WHEN 3 THEN '50米跑'
        WHEN 4 THEN '立定跳远'
        WHEN 5 THEN '坐位体前屈'
        WHEN 6 THEN '仰卧起坐'
        WHEN 7 THEN '引体向上'
        WHEN 8 THEN '800米跑'
        WHEN 9 THEN '1000米跑'
        ELSE '未知项目'
    END as item_name,
    exam_date, exam_semester, exam_year
FROM sys_student_score 
ORDER BY score_id 
LIMIT 10;
