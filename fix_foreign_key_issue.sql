-- 修复外键约束问题

-- 1. 首先检查现有的外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'ry' 
AND TABLE_NAME = 'sys_student_score'
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 2. 检查 sys_exam_item 表是否存在以及其数据
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = 'ry' AND table_name = 'sys_exam_item';

-- 如果表存在，查看现有数据
SELECT * FROM sys_exam_item LIMIT 10;

-- 3. 创建 sys_exam_item 表（如果不存在）
CREATE TABLE IF NOT EXISTS sys_exam_item (
  item_id           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '项目ID',
  item_code         varchar(50)     NOT NULL                   COMMENT '项目编码',
  item_name         varchar(100)    NOT NULL                   COMMENT '项目名称',
  item_type         varchar(20)     DEFAULT 'PHYSICAL'         COMMENT '项目类型',
  unit              varchar(20)     DEFAULT ''                 COMMENT '计量单位',
  description       varchar(500)    DEFAULT ''                 COMMENT '项目描述',
  status            char(1)         DEFAULT '1'                COMMENT '状态(0停用1正常)',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (item_id),
  UNIQUE KEY uk_item_code (item_code)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '考核项目表';

-- 4. 插入考核项目数据
INSERT IGNORE INTO sys_exam_item (item_id, item_code, item_name, item_type, unit, description, status, create_by, create_time) VALUES
(1, 'HEIGHT_WEIGHT', '身高体重', 'PHYSICAL', 'BMI', '测量学生身高和体重，计算BMI指数', '1', 'admin', NOW()),
(2, 'VITAL_CAPACITY', '肺活量', 'PHYSICAL', 'ml', '测量学生肺活量，评估心肺功能', '1', 'admin', NOW()),
(3, 'RUN_50M', '50米跑', 'PHYSICAL', '秒', '测量学生50米跑步时间，评估速度素质', '1', 'admin', NOW()),
(4, 'STANDING_JUMP', '立定跳远', 'PHYSICAL', 'cm', '测量学生立定跳远距离，评估爆发力', '1', 'admin', NOW()),
(5, 'SIT_REACH', '坐位体前屈', 'PHYSICAL', 'cm', '测量学生坐位体前屈距离，评估柔韧性', '1', 'admin', NOW()),
(6, 'SIT_UP', '仰卧起坐', 'PHYSICAL', '个/分钟', '测量学生1分钟仰卧起坐个数，评估腹肌力量', '1', 'admin', NOW()),
(7, 'PULL_UP', '引体向上', 'PHYSICAL', '个', '测量学生引体向上个数，评估上肢力量', '1', 'admin', NOW()),
(8, 'RUN_800M', '800米跑', 'PHYSICAL', '分:秒', '测量学生800米跑步时间，评估耐力素质', '1', 'admin', NOW()),
(9, 'RUN_1000M', '1000米跑', 'PHYSICAL', '分:秒', '测量学生1000米跑步时间，评估耐力素质', '1', 'admin', NOW());

-- 5. 现在可以安全地插入成绩数据
INSERT IGNORE INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(1, 1, 22.5, 'good', 85.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(1, 2, 3200.0, 'excellent', 95.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(1, 3, 8.5, 'good', 88.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试数据'),
(2, 1, 20.8, 'pass', 75.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(2, 2, 2800.0, 'good', 82.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(3, 4, 220.0, 'excellent', 92.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试数据'),
(3, 5, 15.5, 'good', 86.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试数据'),
(4, 6, 45, 'excellent', 94.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试数据'),
(4, 7, 12, 'good', 87.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试数据'),
(5, 8, 240.5, 'pass', 78.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试数据');

-- 6. 验证数据插入
SELECT 
    s.score_id, s.student_id, s.item_id, s.raw_score, s.grade_score, s.score_points,
    e.item_name, e.unit
FROM sys_student_score s
LEFT JOIN sys_exam_item e ON s.item_id = e.item_id
LIMIT 10;
