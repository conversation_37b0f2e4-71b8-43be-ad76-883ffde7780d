-- 简单的测试数据，手动执行

-- 删除现有数据
DELETE FROM sys_student_score WHERE student_id BETWEEN 1 AND 148;

-- 为admin用户(ID=1)添加测试数据
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(1, 1, 22.8, 'good', 85.5, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), 'BMI指数良好'),
(1, 2, 3400.0, 'excellent', 94.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量优秀'),
(1, 3, 8.2, 'good', 87.5, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑良好'),
(1, 4, 235.0, 'excellent', 93.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远优秀'),
(1, 5, 16.8, 'good', 86.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈良好'),
(1, 6, 42, 'good', 84.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐良好'),
(1, 7, 10, 'pass', 76.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上及格'),
(1, 8, 245.3, 'pass', 78.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑及格'),
(1, 9, 285.6, 'good', 82.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑良好');

-- 为几个学生用户添加测试数据
-- 罗明会 (101)
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(101, 1, 21.5, 'good', 82.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试'),
(101, 2, 3100.0, 'good', 86.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试'),
(101, 3, 8.8, 'pass', 74.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试'),
(101, 4, 215.0, 'good', 84.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试'),
(101, 5, 14.5, 'pass', 73.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试'),
(101, 6, 38, 'pass', 76.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试'),
(101, 7, 8, 'pass', 72.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试'),
(101, 8, 260.5, 'pass', 75.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试'),
(101, 9, 310.2, 'pass', 74.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑测试');

-- admin123老师 (102)
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(102, 1, 23.2, 'excellent', 91.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试'),
(102, 2, 3600.0, 'excellent', 96.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试'),
(102, 3, 7.8, 'excellent', 94.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试'),
(102, 4, 245.0, 'excellent', 93.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试'),
(102, 5, 18.2, 'excellent', 92.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试'),
(102, 6, 48, 'excellent', 95.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试'),
(102, 7, 14, 'excellent', 96.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试'),
(102, 8, 220.8, 'excellent', 94.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试'),
(102, 9, 265.5, 'excellent', 93.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑测试');

-- 邓少威 (111)
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
(111, 1, 22.5, 'good', 84.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试'),
(111, 2, 3300.0, 'good', 89.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试'),
(111, 3, 8.3, 'good', 88.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试'),
(111, 4, 230.0, 'good', 87.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远测试'),
(111, 5, 16.5, 'good', 85.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈测试'),
(111, 6, 44, 'good', 86.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐测试'),
(111, 7, 12, 'good', 85.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上测试'),
(111, 8, 240.2, 'good', 84.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑测试'),
(111, 9, 285.5, 'good', 83.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑测试');

-- 验证数据
SELECT 
    student_id,
    CASE item_id 
        WHEN 1 THEN '身高体重'
        WHEN 2 THEN '肺活量'
        WHEN 3 THEN '50米跑'
        WHEN 4 THEN '立定跳远'
        WHEN 5 THEN '坐位体前屈'
        WHEN 6 THEN '仰卧起坐'
        WHEN 7 THEN '引体向上'
        WHEN 8 THEN '800米跑'
        WHEN 9 THEN '1000米跑'
        ELSE '未知项目'
    END as item_name,
    raw_score,
    grade_score,
    score_points
FROM sys_student_score 
WHERE student_id IN (1, 101, 102, 111)
ORDER BY student_id, item_id;
