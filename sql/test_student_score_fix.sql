-- 测试学生成绩表修复SQL
-- 检查表是否存在并创建测试数据

-- 1. 检查sys_student_score表是否存在
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'sys_student_score';

-- 2. 如果表不存在，创建表
CREATE TABLE IF NOT EXISTS sys_student_score (
  score_id          bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '成绩ID',
  student_id        bigint(20)      NOT NULL                   COMMENT '学生ID',
  item_id           bigint(20)      DEFAULT NULL               COMMENT '考核项目ID',
  criterion_id      bigint(20)      DEFAULT NULL               COMMENT '使用的标准ID',
  raw_score         decimal(10,2)   DEFAULT NULL               COMMENT '原始成绩',
  grade_score       varchar(20)     DEFAULT ''                 COMMENT '等级成绩(excellent,good,pass,fail)',
  score_points      decimal(5,2)    DEFAULT NULL               COMMENT '分数(百分制)',
  exam_date         date            DEFAULT NULL               COMMENT '考试日期',
  exam_semester     varchar(20)     DEFAULT ''                 COMMENT '考试学期',
  exam_year         bigint(20)      DEFAULT NULL               COMMENT '考试年份',
  import_batch      varchar(50)     DEFAULT ''                 COMMENT '导入批次号',
  is_manual         char(1)         DEFAULT '0'                COMMENT '是否人工录入(0自动导入1人工录入)',
  status            char(1)         DEFAULT '1'                COMMENT '状态(0无效1有效)',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime        DEFAULT NULL               COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime        DEFAULT NULL               COMMENT '更新时间',
  remark            varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (score_id),
  KEY idx_student_id (student_id),
  KEY idx_item_id (item_id),
  KEY idx_exam_date (exam_date),
  KEY idx_exam_semester (exam_semester),
  KEY idx_status (status)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '学生成绩表';

-- 3. 插入测试数据（使用现有的sys_user表中的用户ID）
INSERT IGNORE INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
-- 假设用户ID 1 和 2 存在于sys_user表中
(1, 1, 22.5, 'good', 85.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(1, 2, 3200.0, 'excellent', 95.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量测试数据'),
(2, 1, 20.8, 'pass', 75.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '身高体重测试数据'),
(2, 3, 8.5, 'good', 88.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑测试数据');

-- 4. 测试查询（验证关联查询是否正常工作）
SELECT 
    s.score_id, s.student_id, s.item_id, s.raw_score, s.grade_score, s.score_points,
    u.user_name as student_name, u.login_name as student_number,
    d.dept_name as class_name,
    CASE s.item_id 
        WHEN 1 THEN '身高体重'
        WHEN 2 THEN '肺活量'
        WHEN 3 THEN '50米跑'
        WHEN 4 THEN '立定跳远'
        WHEN 5 THEN '坐位体前屈'
        WHEN 6 THEN '仰卧起坐'
        WHEN 7 THEN '引体向上'
        WHEN 8 THEN '800米跑'
        WHEN 9 THEN '1000米跑'
        ELSE '未知项目'
    END as item_name
FROM sys_student_score s
LEFT JOIN sys_user u ON s.student_id = u.user_id
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
LIMIT 10;

-- 5. 检查现有用户数据
SELECT user_id, user_name, login_name, dept_id FROM sys_user LIMIT 5;

-- 6. 检查部门数据
SELECT dept_id, dept_name FROM sys_dept LIMIT 5;
