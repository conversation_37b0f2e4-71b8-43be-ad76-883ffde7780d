-- 学生个人成绩模块菜单权限SQL

-- 删除可能存在的旧菜单
DELETE FROM sys_role_menu WHERE menu_id IN (6000, 6001, 6002, 6003);
DELETE FROM sys_menu WHERE menu_id IN (6000, 6001, 6002, 6003);

-- 创建学生个人成绩主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(6000, '我的成绩', '0', '7', '/system/personalScore', '', 'C', '0', '1', 'system:personalScore:view', 'fa fa-trophy', 'admin', now(), '学生个人成绩查看');

-- 创建功能按钮
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, url, target, menu_type, visible, is_refresh, perms, icon, create_by, create_time, remark) VALUES
(6001, '个人成绩查询', 6000, 1, '#', '', 'F', '0', '1', 'system:personalScore:list', '#', 'admin', now(), ''),
(6002, '个人成绩导出', 6000, 2, '#', '', 'F', '0', '1', 'system:personalScore:export', '#', 'admin', now(), ''),
(6003, '成绩统计查看', 6000, 3, '#', '', 'F', '0', '1', 'system:personalScore:statistics', '#', 'admin', now(), '');

-- 为管理员角色分配权限（测试用）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 6000),
(1, 6001),
(1, 6002),
(1, 6003);

-- 创建学生角色（如果不存在）
INSERT IGNORE INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
(100, '学生', 'student', '3', '5', '1', '1', '0', '0', 'admin', now(), '学生角色');

-- 为学生角色分配个人成绩权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
(100, 6000),
(100, 6001),
(100, 6002),
(100, 6003);

-- 验证菜单是否创建成功
SELECT menu_id, menu_name, url, perms, menu_type FROM sys_menu WHERE menu_id IN (6000, 6001, 6002, 6003) ORDER BY menu_id;

-- 查看角色权限分配
SELECT r.role_name, m.menu_name, m.perms 
FROM sys_role r 
JOIN sys_role_menu rm ON r.role_id = rm.role_id 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE m.menu_id IN (6000, 6001, 6002, 6003)
ORDER BY r.role_id, m.menu_id;
