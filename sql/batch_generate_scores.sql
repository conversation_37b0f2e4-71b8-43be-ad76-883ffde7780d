-- 批量为所有学生生成成绩数据的简化版本

-- 删除现有数据
DELETE FROM sys_student_score WHERE student_id BETWEEN 101 AND 148;

-- 使用存储过程批量生成数据
DELIMITER $$

CREATE PROCEDURE GenerateStudentScores()
BEGIN
    DECLARE student_id_var INT DEFAULT 101;
    DECLARE item_id_var INT;
    DECLARE raw_score_var DECIMAL(10,2);
    DECLARE grade_var VARCHAR(20);
    DECLARE points_var DECIMAL(5,2);
    
    -- 为每个学生生成成绩
    WHILE student_id_var <= 148 DO
        SET item_id_var = 1;
        
        -- 为每个项目生成成绩
        WHILE item_id_var <= 9 DO
            -- 根据项目生成不同的成绩范围
            CASE item_id_var
                WHEN 1 THEN -- 身高体重
                    SET raw_score_var = 18.0 + (RAND() * 8.0); -- 18-26
                    SET points_var = 70 + (RAND() * 25);
                WHEN 2 THEN -- 肺活量
                    SET raw_score_var = 2500 + (RAND() * 1500); -- 2500-4000
                    SET points_var = 70 + (RAND() * 25);
                WHEN 3 THEN -- 50米跑
                    SET raw_score_var = 7.0 + (RAND() * 3.0); -- 7-10秒
                    SET points_var = 70 + (RAND() * 25);
                WHEN 4 THEN -- 立定跳远
                    SET raw_score_var = 160 + (RAND() * 100); -- 160-260cm
                    SET points_var = 70 + (RAND() * 25);
                WHEN 5 THEN -- 坐位体前屈
                    SET raw_score_var = 10 + (RAND() * 15); -- 10-25cm
                    SET points_var = 70 + (RAND() * 25);
                WHEN 6 THEN -- 仰卧起坐
                    SET raw_score_var = 25 + (RAND() * 30); -- 25-55个
                    SET points_var = 70 + (RAND() * 25);
                WHEN 7 THEN -- 引体向上
                    IF student_id_var IN (103, 109, 110, 119, 124, 126, 131, 140, 146) THEN
                        -- 女学生引体向上设为0
                        SET raw_score_var = 0;
                        SET points_var = 0;
                    ELSE
                        SET raw_score_var = 3 + (RAND() * 15); -- 3-18个
                        SET points_var = 65 + (RAND() * 30);
                    END IF;
                WHEN 8 THEN -- 800米跑
                    SET raw_score_var = 200 + (RAND() * 100); -- 200-300秒
                    SET points_var = 70 + (RAND() * 25);
                WHEN 9 THEN -- 1000米跑
                    IF student_id_var IN (103, 109, 110, 119, 124, 126, 131, 140, 146) THEN
                        -- 女学生1000米跑设为0
                        SET raw_score_var = 0;
                        SET points_var = 0;
                    ELSE
                        SET raw_score_var = 240 + (RAND() * 120); -- 240-360秒
                        SET points_var = 65 + (RAND() * 30);
                    END IF;
            END CASE;
            
            -- 根据分数确定等级
            IF points_var >= 90 THEN
                SET grade_var = 'excellent';
            ELSEIF points_var >= 80 THEN
                SET grade_var = 'good';
            ELSEIF points_var >= 60 THEN
                SET grade_var = 'pass';
            ELSE
                SET grade_var = 'fail';
            END IF;
            
            -- 插入数据
            INSERT INTO sys_student_score (
                student_id, item_id, raw_score, grade_score, score_points, 
                exam_date, exam_semester, exam_year, is_manual, status, 
                create_by, create_time, remark
            ) VALUES (
                student_id_var, item_id_var, raw_score_var, grade_var, points_var,
                DATE_ADD('2025-01-15', INTERVAL (item_id_var - 1) DAY), '2025-1', 2025, '1', '1',
                'admin', NOW(), CONCAT('批量生成的测试数据')
            );
            
            SET item_id_var = item_id_var + 1;
        END WHILE;
        
        SET student_id_var = student_id_var + 1;
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程
CALL GenerateStudentScores();

-- 删除存储过程
DROP PROCEDURE GenerateStudentScores;

-- 验证数据
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT student_id) as student_count,
    COUNT(DISTINCT item_id) as item_count
FROM sys_student_score 
WHERE student_id BETWEEN 101 AND 148;

-- 查看部分数据样例
SELECT 
    s.student_id,
    CASE s.item_id 
        WHEN 1 THEN '身高体重'
        WHEN 2 THEN '肺活量'
        WHEN 3 THEN '50米跑'
        WHEN 4 THEN '立定跳远'
        WHEN 5 THEN '坐位体前屈'
        WHEN 6 THEN '仰卧起坐'
        WHEN 7 THEN '引体向上'
        WHEN 8 THEN '800米跑'
        WHEN 9 THEN '1000米跑'
    END as item_name,
    s.raw_score,
    s.grade_score,
    s.score_points
FROM sys_student_score s 
WHERE s.student_id IN (101, 105, 110, 120, 130, 140, 148)
ORDER BY s.student_id, s.item_id
LIMIT 50;
