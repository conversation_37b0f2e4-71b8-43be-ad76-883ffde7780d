-- 为admin用户（user_id=1）添加测试成绩数据

-- 删除可能存在的admin用户成绩数据
DELETE FROM sys_student_score WHERE student_id = 1;

-- 为admin用户添加各个项目的成绩数据
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
-- admin用户的成绩数据
(1, 1, 22.8, 'good', 85.5, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), 'BMI指数良好'),
(1, 2, 3400.0, 'excellent', 94.0, '2025-01-15', '2025-1', 2025, '1', '1', 'admin', NOW(), '肺活量优秀'),
(1, 3, 8.2, 'good', 87.5, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '50米跑良好'),
(1, 4, 235.0, 'excellent', 93.0, '2025-01-16', '2025-1', 2025, '1', '1', 'admin', NOW(), '立定跳远优秀'),
(1, 5, 16.8, 'good', 86.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '坐位体前屈良好'),
(1, 6, 42, 'good', 84.0, '2025-01-17', '2025-1', 2025, '1', '1', 'admin', NOW(), '仰卧起坐良好'),
(1, 7, 10, 'pass', 76.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '引体向上及格'),
(1, 8, 245.3, 'pass', 78.0, '2025-01-18', '2025-1', 2025, '1', '1', 'admin', NOW(), '800米跑及格'),
(1, 9, 285.6, 'good', 82.0, '2025-01-19', '2025-1', 2025, '1', '1', 'admin', NOW(), '1000米跑良好');

-- 添加一些历史学期的数据
INSERT INTO sys_student_score (
    student_id, item_id, raw_score, grade_score, score_points, 
    exam_date, exam_semester, exam_year, is_manual, status, 
    create_by, create_time, remark
) VALUES 
-- 2024年第二学期的数据
(1, 1, 23.1, 'good', 83.0, '2024-12-15', '2024-2', 2024, '1', '1', 'admin', NOW(), '期末体测'),
(1, 2, 3200.0, 'good', 88.0, '2024-12-15', '2024-2', 2024, '1', '1', 'admin', NOW(), '期末体测'),
(1, 3, 8.8, 'pass', 75.0, '2024-12-16', '2024-2', 2024, '1', '1', 'admin', NOW(), '期末体测'),
-- 2024年第一学期的数据
(1, 4, 210.0, 'good', 85.0, '2024-06-15', '2024-1', 2024, '1', '1', 'admin', NOW(), '期中体测'),
(1, 5, 14.2, 'pass', 72.0, '2024-06-15', '2024-1', 2024, '1', '1', 'admin', NOW(), '期中体测');

-- 验证数据插入
SELECT 
    student_id,
    CASE item_id 
        WHEN 1 THEN '身高体重'
        WHEN 2 THEN '肺活量'
        WHEN 3 THEN '50米跑'
        WHEN 4 THEN '立定跳远'
        WHEN 5 THEN '坐位体前屈'
        WHEN 6 THEN '仰卧起坐'
        WHEN 7 THEN '引体向上'
        WHEN 8 THEN '800米跑'
        WHEN 9 THEN '1000米跑'
        ELSE '未知项目'
    END as item_name,
    raw_score,
    CASE grade_score
        WHEN 'excellent' THEN '优秀'
        WHEN 'good' THEN '良好'
        WHEN 'pass' THEN '及格'
        WHEN 'fail' THEN '不及格'
        ELSE grade_score
    END as grade_name,
    score_points,
    exam_date,
    exam_semester,
    exam_year
FROM sys_student_score 
WHERE student_id = 1 
ORDER BY exam_year DESC, exam_semester DESC, item_id;

-- 统计信息
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN grade_score = 'excellent' THEN 1 ELSE 0 END) as excellent_count,
    SUM(CASE WHEN grade_score = 'good' THEN 1 ELSE 0 END) as good_count,
    SUM(CASE WHEN grade_score = 'pass' THEN 1 ELSE 0 END) as pass_count,
    SUM(CASE WHEN grade_score = 'fail' THEN 1 ELSE 0 END) as fail_count,
    ROUND(AVG(score_points), 2) as avg_score
FROM sys_student_score 
WHERE student_id = 1;
