<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysStudentScoreMapper">
    
    <resultMap type="SysStudentScore" id="SysStudentScoreResult">
        <result property="scoreId"    column="score_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="criterionId"    column="criterion_id"    />
        <result property="rawScore"    column="raw_score"    />
        <result property="gradeScore"    column="grade_score"    />
        <result property="scorePoints"    column="score_points"    />
        <result property="examDate"    column="exam_date"    />
        <result property="examSemester"    column="exam_semester"    />
        <result property="examYear"    column="exam_year"    />
        <result property="importBatch"    column="import_batch"    />
        <result property="isManual"    column="is_manual"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <!-- 关联查询字段 -->
        <result property="studentName"    column="student_name"    />
        <result property="studentNumber"    column="student_number"    />
        <result property="className"    column="class_name"    />
        <result property="majorName"    column="major_name"    />
        <result property="collegeName"    column="college_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemUnit"    column="item_unit"    />
        <result property="studentGender"    column="student_gender"    />
        <result property="gradeLevel"    column="grade_level"    />
    </resultMap>

    <sql id="selectSysStudentScoreVo">
        select
            s.score_id, s.student_id, s.item_id, s.criterion_id, s.raw_score, s.grade_score, s.score_points,
            s.exam_date, s.exam_semester, s.exam_year, s.import_batch, s.is_manual, s.status,
            s.create_by, s.create_time, s.update_by, s.update_time, s.remark,
            -- 学生信息
            st.student_name, st.student_number, st.gender as student_gender, st.grade_level,
            -- 班级信息
            c.class_name, c.major_name, c.college_name,
            -- 考核项目信息
            i.item_name, i.item_code, i.unit as item_unit
        from sys_student_score s
        left join sys_student st on s.student_id = st.student_id
        left join sys_class c on st.class_id = c.class_id
        left join sys_test_item i on s.item_id = i.item_id
    </sql>

    <select id="selectSysStudentScoreList" parameterType="SysStudentScore" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        <where>
            <if test="studentId != null "> and s.student_id = #{studentId}</if>
            <if test="itemId != null "> and s.item_id = #{itemId}</if>
            <if test="criterionId != null "> and s.criterion_id = #{criterionId}</if>
            <if test="rawScore != null "> and s.raw_score = #{rawScore}</if>
            <if test="gradeScore != null  and gradeScore != ''"> and s.grade_score = #{gradeScore}</if>
            <if test="scorePoints != null "> and s.score_points = #{scorePoints}</if>
            <if test="examDate != null "> and s.exam_date = #{examDate}</if>
            <if test="examSemester != null  and examSemester != ''"> and s.exam_semester = #{examSemester}</if>
            <if test="examYear != null "> and s.exam_year = #{examYear}</if>
            <if test="importBatch != null  and importBatch != ''"> and s.import_batch = #{importBatch}</if>
            <if test="isManual != null  and isManual != ''"> and s.is_manual = #{isManual}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
            <!-- 关联查询条件 -->
            <if test="studentName != null and studentName != ''"> and st.student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentNumber != null and studentNumber != ''"> and st.student_number like concat('%', #{studentNumber}, '%')</if>
            <if test="className != null and className != ''"> and c.class_name like concat('%', #{className}, '%')</if>
            <if test="majorName != null and majorName != ''"> and c.major_name like concat('%', #{majorName}, '%')</if>
            <if test="collegeName != null and collegeName != ''"> and c.college_name like concat('%', #{collegeName}, '%')</if>
            <if test="itemName != null and itemName != ''"> and i.item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemCode != null and itemCode != ''"> and i.item_code = #{itemCode}</if>
            <if test="studentGender != null and studentGender != ''"> and st.gender = #{studentGender}</if>
            <if test="gradeLevel != null and gradeLevel != ''"> and st.grade_level = #{gradeLevel}</if>
        </where>
        order by s.create_time desc
    </select>
    
    <select id="selectSysStudentScoreByScoreId" parameterType="Long" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        where s.score_id = #{scoreId}
    </select>

    <insert id="insertSysStudentScore" parameterType="SysStudentScore" useGeneratedKeys="true" keyProperty="scoreId">
        insert into sys_student_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            item_id,
            <if test="criterionId != null">criterion_id,</if>
            <if test="rawScore != null">raw_score,</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score,</if>
            <if test="scorePoints != null">score_points,</if>
            <if test="examDate != null">exam_date,</if>
            <if test="examSemester != null and examSemester != ''">exam_semester,</if>
            <if test="examYear != null">exam_year,</if>
            <if test="importBatch != null">import_batch,</if>
            <if test="isManual != null">is_manual,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            #{itemId},
            <if test="criterionId != null">#{criterionId},</if>
            <if test="rawScore != null">#{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">#{gradeScore},</if>
            <if test="scorePoints != null">#{scorePoints},</if>
            <if test="examDate != null">#{examDate},</if>
            <if test="examSemester != null and examSemester != ''">#{examSemester},</if>
            <if test="examYear != null">#{examYear},</if>
            <if test="importBatch != null">#{importBatch},</if>
            <if test="isManual != null">#{isManual},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysStudentScore" parameterType="SysStudentScore">
        update sys_student_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="criterionId != null">criterion_id = #{criterionId},</if>
            <if test="rawScore != null">raw_score = #{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score = #{gradeScore},</if>
            <if test="scorePoints != null">score_points = #{scorePoints},</if>
            <if test="examDate != null">exam_date = #{examDate},</if>
            <if test="examSemester != null and examSemester != ''">exam_semester = #{examSemester},</if>
            <if test="examYear != null">exam_year = #{examYear},</if>
            <if test="importBatch != null">import_batch = #{importBatch},</if>
            <if test="isManual != null">is_manual = #{isManual},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where score_id = #{scoreId}
    </update>

    <delete id="deleteSysStudentScoreByScoreId" parameterType="Long">
        delete from sys_student_score where score_id = #{scoreId}
    </delete>

    <delete id="deleteSysStudentScoreByScoreIds" parameterType="String">
        delete from sys_student_score where score_id in 
        <foreach item="scoreId" collection="array" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
    </delete>

</mapper>