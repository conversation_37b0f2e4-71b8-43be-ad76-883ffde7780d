<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysStudentScoreMapper">
    
    <resultMap type="SysStudentScore" id="SysStudentScoreResult">
        <result property="scoreId"    column="score_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="criterionId"    column="criterion_id"    />
        <result property="rawScore"    column="raw_score"    />
        <result property="gradeScore"    column="grade_score"    />
        <result property="scorePoints"    column="score_points"    />
        <result property="examDate"    column="exam_date"    />
        <result property="examSemester"    column="exam_semester"    />
        <result property="examYear"    column="exam_year"    />
        <result property="importBatch"    column="import_batch"    />
        <result property="isManual"    column="is_manual"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <!-- 关联查询字段 -->
        <result property="studentName"    column="student_name"    />
        <result property="studentNumber"    column="student_number"    />
        <result property="className"    column="class_name"    />
        <result property="majorName"    column="major_name"    />
        <result property="collegeName"    column="college_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemUnit"    column="item_unit"    />
        <result property="studentGender"    column="student_gender"    />
        <result property="gradeLevel"    column="grade_level"    />
    </resultMap>

    <sql id="selectSysStudentScoreVo">
        select
            score_id, student_id, item_id, criterion_id, raw_score, grade_score, score_points,
            exam_date, exam_semester, exam_year, import_batch, is_manual, status,
            create_by, create_time, update_by, update_time, remark,
            -- 模拟关联字段，不做实际关联查询
            CONCAT('学生', student_id) as student_name,
            CONCAT('STU', student_id) as student_number,
            'M' as student_gender,
            CONCAT('班级', student_id) as class_name,
            CONCAT('专业', student_id) as major_name,
            '计算机学院' as college_name,
            CASE item_id
                WHEN 1 THEN '身高体重'
                WHEN 2 THEN '肺活量'
                WHEN 3 THEN '50米跑'
                WHEN 4 THEN '立定跳远'
                WHEN 5 THEN '坐位体前屈'
                WHEN 6 THEN '仰卧起坐'
                WHEN 7 THEN '引体向上'
                WHEN 8 THEN '800米跑'
                WHEN 9 THEN '1000米跑'
                ELSE '未知项目'
            END as item_name,
            CONCAT('ITEM_', item_id) as item_code,
            CASE item_id
                WHEN 1 THEN 'BMI'
                WHEN 2 THEN 'ml'
                WHEN 3 THEN '秒'
                WHEN 4 THEN 'cm'
                WHEN 5 THEN 'cm'
                WHEN 6 THEN '个/分钟'
                WHEN 7 THEN '个'
                WHEN 8 THEN '分:秒'
                WHEN 9 THEN '分:秒'
                ELSE ''
            END as item_unit,
            'freshman' as grade_level
        from sys_student_score
    </sql>

    <select id="selectSysStudentScoreList" parameterType="SysStudentScore" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        <where>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="criterionId != null "> and criterion_id = #{criterionId}</if>
            <if test="rawScore != null "> and raw_score = #{rawScore}</if>
            <if test="gradeScore != null  and gradeScore != ''"> and grade_score = #{gradeScore}</if>
            <if test="scorePoints != null "> and score_points = #{scorePoints}</if>
            <if test="examDate != null "> and exam_date = #{examDate}</if>
            <if test="examSemester != null  and examSemester != ''"> and exam_semester = #{examSemester}</if>
            <if test="examYear != null "> and exam_year = #{examYear}</if>
            <if test="importBatch != null  and importBatch != ''"> and import_batch = #{importBatch}</if>
            <if test="isManual != null  and isManual != ''"> and is_manual = #{isManual}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <!-- 简单查询条件，不使用关联 -->
            <if test="studentName != null and studentName != ''"> and CONCAT('学生', student_id) like concat('%', #{studentName}, '%')</if>
            <if test="studentNumber != null and studentNumber != ''"> and CONCAT('STU', student_id) like concat('%', #{studentNumber}, '%')</if>
            <if test="className != null and className != ''"> and CONCAT('班级', student_id) like concat('%', #{className}, '%')</if>
            <if test="majorName != null and majorName != ''"> and CONCAT('专业', student_id) like concat('%', #{majorName}, '%')</if>
            <if test="collegeName != null and collegeName != ''"> and '计算机学院' like concat('%', #{collegeName}, '%')</if>
            <if test="itemName != null and itemName != ''"> and (
                (#{itemName} = '身高体重' and item_id = 1) or
                (#{itemName} = '肺活量' and item_id = 2) or
                (#{itemName} = '50米跑' and item_id = 3) or
                (#{itemName} = '立定跳远' and item_id = 4) or
                (#{itemName} = '坐位体前屈' and item_id = 5) or
                (#{itemName} = '仰卧起坐' and item_id = 6) or
                (#{itemName} = '引体向上' and item_id = 7) or
                (#{itemName} = '800米跑' and item_id = 8) or
                (#{itemName} = '1000米跑' and item_id = 9)
            )</if>
            <if test="itemCode != null and itemCode != ''"> and CONCAT('ITEM_', item_id) = #{itemCode}</if>
            <if test="studentGender != null and studentGender != ''"> and 'M' = #{studentGender}</if>
            <if test="gradeLevel != null and gradeLevel != ''"> and 'freshman' = #{gradeLevel}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysStudentScoreByScoreId" parameterType="Long" resultMap="SysStudentScoreResult">
        <include refid="selectSysStudentScoreVo"/>
        where score_id = #{scoreId}
    </select>

    <insert id="insertSysStudentScore" parameterType="SysStudentScore" useGeneratedKeys="true" keyProperty="scoreId">
        insert into sys_student_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            item_id,
            <if test="criterionId != null">criterion_id,</if>
            <if test="rawScore != null">raw_score,</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score,</if>
            <if test="scorePoints != null">score_points,</if>
            <if test="examDate != null">exam_date,</if>
            <if test="examSemester != null and examSemester != ''">exam_semester,</if>
            <if test="examYear != null">exam_year,</if>
            <if test="importBatch != null">import_batch,</if>
            <if test="isManual != null">is_manual,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            #{itemId},
            <if test="criterionId != null">#{criterionId},</if>
            <if test="rawScore != null">#{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">#{gradeScore},</if>
            <if test="scorePoints != null">#{scorePoints},</if>
            <if test="examDate != null">#{examDate},</if>
            <if test="examSemester != null and examSemester != ''">#{examSemester},</if>
            <if test="examYear != null">#{examYear},</if>
            <if test="importBatch != null">#{importBatch},</if>
            <if test="isManual != null">#{isManual},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysStudentScore" parameterType="SysStudentScore">
        update sys_student_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="criterionId != null">criterion_id = #{criterionId},</if>
            <if test="rawScore != null">raw_score = #{rawScore},</if>
            <if test="gradeScore != null and gradeScore != ''">grade_score = #{gradeScore},</if>
            <if test="scorePoints != null">score_points = #{scorePoints},</if>
            <if test="examDate != null">exam_date = #{examDate},</if>
            <if test="examSemester != null and examSemester != ''">exam_semester = #{examSemester},</if>
            <if test="examYear != null">exam_year = #{examYear},</if>
            <if test="importBatch != null">import_batch = #{importBatch},</if>
            <if test="isManual != null">is_manual = #{isManual},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where score_id = #{scoreId}
    </update>

    <delete id="deleteSysStudentScoreByScoreId" parameterType="Long">
        delete from sys_student_score where score_id = #{scoreId}
    </delete>

    <delete id="deleteSysStudentScoreByScoreIds" parameterType="String">
        delete from sys_student_score where score_id in 
        <foreach item="scoreId" collection="array" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
    </delete>

</mapper>