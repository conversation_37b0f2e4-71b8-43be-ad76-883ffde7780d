package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 学生成绩对象 sys_student_score
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public class SysStudentScore extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 成绩ID */
    private Long scoreId;

    /** 学生ID */
    private Long studentId;

    /** 考核项目ID */
    private Long itemId;

    /** 使用的标准ID */
    private Long criterionId;

    /** 原始成绩 */
    private BigDecimal rawScore;

    /** 等级成绩(excellent,good,pass,fail) */
    private String gradeScore;

    /** 分数(百分制) */
    private BigDecimal scorePoints;

    /** 考试日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "考试日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date examDate;

    /** 考试学期 */
    @Excel(name = "考试学期")
    private String examSemester;

    /** 考试年份 */
    @Excel(name = "考试年份")
    private Long examYear;

    /** 导入批次号 */
    private String importBatch;

    /** 是否人工录入(0自动导入1人工录入) */
    private String isManual;

    /** 状态(0无效1有效) */
    @Excel(name = "状态(0无效1有效)")
    private String status;

    // ========== 关联查询字段 ==========

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String studentName;

    /** 学生学号 */
    @Excel(name = "学号")
    private String studentNumber;

    /** 班级名称 */
    @Excel(name = "班级")
    private String className;

    /** 专业名称 */
    @Excel(name = "专业")
    private String majorName;

    /** 学院名称 */
    @Excel(name = "学院")
    private String collegeName;

    /** 考核项目名称 */
    @Excel(name = "考核项目")
    private String itemName;

    /** 考核项目编码 */
    private String itemCode;

    /** 项目单位 */
    private String itemUnit;

    /** 学生性别 */
    private String studentGender;

    /** 学生年级 */
    private String gradeLevel;

    public void setScoreId(Long scoreId) 
    {
        this.scoreId = scoreId;
    }

    public Long getScoreId() 
    {
        return scoreId;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }

    public void setCriterionId(Long criterionId) 
    {
        this.criterionId = criterionId;
    }

    public Long getCriterionId() 
    {
        return criterionId;
    }

    public void setRawScore(BigDecimal rawScore) 
    {
        this.rawScore = rawScore;
    }

    public BigDecimal getRawScore() 
    {
        return rawScore;
    }

    public void setGradeScore(String gradeScore) 
    {
        this.gradeScore = gradeScore;
    }

    public String getGradeScore() 
    {
        return gradeScore;
    }

    public void setScorePoints(BigDecimal scorePoints) 
    {
        this.scorePoints = scorePoints;
    }

    public BigDecimal getScorePoints() 
    {
        return scorePoints;
    }

    public void setExamDate(Date examDate) 
    {
        this.examDate = examDate;
    }

    public Date getExamDate() 
    {
        return examDate;
    }

    public void setExamSemester(String examSemester) 
    {
        this.examSemester = examSemester;
    }

    public String getExamSemester() 
    {
        return examSemester;
    }

    public void setExamYear(Long examYear) 
    {
        this.examYear = examYear;
    }

    public Long getExamYear() 
    {
        return examYear;
    }

    public void setImportBatch(String importBatch) 
    {
        this.importBatch = importBatch;
    }

    public String getImportBatch() 
    {
        return importBatch;
    }

    public void setIsManual(String isManual) 
    {
        this.isManual = isManual;
    }

    public String getIsManual() 
    {
        return isManual;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    // ========== 关联查询字段的getter/setter方法 ==========

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getStudentNumber() {
        return studentNumber;
    }

    public void setStudentNumber(String studentNumber) {
        this.studentNumber = studentNumber;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemUnit() {
        return itemUnit;
    }

    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit;
    }

    public String getStudentGender() {
        return studentGender;
    }

    public void setStudentGender(String studentGender) {
        this.studentGender = studentGender;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scoreId", getScoreId())
            .append("studentId", getStudentId())
            .append("itemId", getItemId())
            .append("criterionId", getCriterionId())
            .append("rawScore", getRawScore())
            .append("gradeScore", getGradeScore())
            .append("scorePoints", getScorePoints())
            .append("examDate", getExamDate())
            .append("examSemester", getExamSemester())
            .append("examYear", getExamYear())
            .append("importBatch", getImportBatch())
            .append("isManual", getIsManual())
            .append("status", getStatus())
            .append("studentName", getStudentName())
            .append("studentNumber", getStudentNumber())
            .append("className", getClassName())
            .append("majorName", getMajorName())
            .append("collegeName", getCollegeName())
            .append("itemName", getItemName())
            .append("itemCode", getItemCode())
            .append("itemUnit", getItemUnit())
            .append("studentGender", getStudentGender())
            .append("gradeLevel", getGradeLevel())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
