<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增学生成绩')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-score-add">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">学生ID：</label>
                        <div class="col-sm-8">
                            <input name="studentId" class="form-control" type="number" required placeholder="请输入学生ID">
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请输入学生的数据库ID</span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">考核项目：</label>
                        <div class="col-sm-8">
                            <select name="itemId" class="form-control" required>
                                <option value="">请选择考核项目</option>
                                <option value="1">身高体重</option>
                                <option value="2">肺活量</option>
                                <option value="3">50米跑</option>
                                <option value="4">立定跳远</option>
                                <option value="5">坐位体前屈</option>
                                <option value="6">仰卧起坐</option>
                                <option value="7">引体向上</option>
                                <option value="8">800米跑</option>
                                <option value="9">1000米跑</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">原始成绩：</label>
                        <div class="col-sm-8">
                            <input name="rawScore" class="form-control" type="number" step="0.01" required placeholder="请输入原始成绩">
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 实际测试结果</span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">百分制分数：</label>
                        <div class="col-sm-8">
                            <input name="scorePoints" class="form-control" type="number" step="0.01" min="0" max="100" placeholder="请输入百分制分数">
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 0-100分</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">成绩等级：</label>
                        <div class="col-sm-8">
                            <select name="gradeScore" class="form-control" required>
                                <option value="">请选择成绩等级</option>
                                <option value="excellent">优秀</option>
                                <option value="good">良好</option>
                                <option value="pass">及格</option>
                                <option value="fail">不及格</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">标准ID：</label>
                        <div class="col-sm-8">
                            <input name="criterionId" class="form-control" type="number" placeholder="请输入评分标准ID">
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选，用于关联评分标准</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">考试学期：</label>
                        <div class="col-sm-8">
                            <select name="examSemester" class="form-control" required>
                                <option value="">请选择考试学期</option>
                                <option value="2025-1">2025年第一学期</option>
                                <option value="2025-2">2025年第二学期</option>
                                <option value="2024-1">2024年第一学期</option>
                                <option value="2024-2">2024年第二学期</option>
                                <option value="2023-1">2023年第一学期</option>
                                <option value="2023-2">2023年第二学期</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">考试年份：</label>
                        <div class="col-sm-8">
                            <select name="examYear" class="form-control" required>
                                <option value="">请选择考试年份</option>
                                <option value="2025">2025年</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">考试日期：</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <input name="examDate" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">录入方式：</label>
                        <div class="col-sm-8">
                            <select name="isManual" class="form-control">
                                <option value="1" selected>人工录入</option>
                                <option value="0">自动导入</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">导入批次：</label>
                        <div class="col-sm-8">
                            <input name="importBatch" class="form-control" type="text" placeholder="请输入导入批次号">
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 批量导入时的批次标识</span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">状态：</label>
                        <div class="col-sm-8">
                            <select name="status" class="form-control">
                                <option value="1" selected>有效</option>
                                <option value="0">无效</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <textarea name="remark" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/score"
        $("#form-score-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-score-add').serialize());
            }
        }

        $("input[name='examDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>