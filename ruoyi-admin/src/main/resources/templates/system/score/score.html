<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学生成绩列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学生姓名：</label>
                                <input type="text" name="studentName" placeholder="请输入学生姓名"/>
                            </li>
                            <li>
                                <label>学号：</label>
                                <input type="text" name="studentNumber" placeholder="请输入学号"/>
                            </li>
                            <li>
                                <label>班级：</label>
                                <input type="text" name="className" placeholder="请输入班级名称"/>
                            </li>
                            <li>
                                <label>专业：</label>
                                <input type="text" name="majorName" placeholder="请输入专业名称"/>
                            </li>
                            <li>
                                <label>考核项目：</label>
                                <select name="itemName" class="form-control">
                                    <option value="">全部项目</option>
                                    <option value="身高体重">身高体重</option>
                                    <option value="肺活量">肺活量</option>
                                    <option value="50米跑">50米跑</option>
                                    <option value="立定跳远">立定跳远</option>
                                    <option value="坐位体前屈">坐位体前屈</option>
                                    <option value="仰卧起坐">仰卧起坐</option>
                                    <option value="引体向上">引体向上</option>
                                    <option value="1000米跑">1000米跑</option>
                                    <option value="800米跑">800米跑</option>
                                </select>
                            </li>
                            <li>
                                <label>考试学期：</label>
                                <select name="examSemester" class="form-control">
                                    <option value="">全部学期</option>
                                    <option value="2025-1">2025年第一学期</option>
                                    <option value="2025-2">2025年第二学期</option>
                                    <option value="2024-1">2024年第一学期</option>
                                    <option value="2024-2">2024年第二学期</option>
                                    <option value="2023-1">2023年第一学期</option>
                                    <option value="2023-2">2023年第二学期</option>
                                </select>
                            </li>
                            <li>
                                <label>成绩等级：</label>
                                <select name="gradeScore" class="form-control">
                                    <option value="">全部等级</option>
                                    <option value="excellent">优秀</option>
                                    <option value="good">良好</option>
                                    <option value="pass">及格</option>
                                    <option value="fail">不及格</option>
                                </select>
                            </li>
                            <li>
                                <label>性别：</label>
                                <select name="studentGender" class="form-control">
                                    <option value="">全部</option>
                                    <option value="M">男</option>
                                    <option value="F">女</option>
                                </select>
                            </li>
                            <li>
                                <label>年级：</label>
                                <select name="gradeLevel" class="form-control">
                                    <option value="">全部年级</option>
                                    <option value="freshman">大一</option>
                                    <option value="sophomore">大二</option>
                                    <option value="junior">大三</option>
                                    <option value="senior">大四</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:score:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:score:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:score:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:score:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:score:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:score:remove')}]];
        var prefix = ctx + "system/score";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "学生成绩",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'scoreId',
                    title: '成绩ID',
                    visible: false
                },
                {
                    field: 'studentName',
                    title: '学生姓名',
                    align: 'center'
                },
                {
                    field: 'studentNumber',
                    title: '学号',
                    align: 'center'
                },
                {
                    field: 'className',
                    title: '班级',
                    align: 'center'
                },
                {
                    field: 'majorName',
                    title: '专业',
                    align: 'center'
                },
                {
                    field: 'itemName',
                    title: '考核项目',
                    align: 'center'
                },
                {
                    field: 'rawScore',
                    title: '原始成绩',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            var unit = row.itemUnit ? row.itemUnit : '';
                            return '<span class="text-primary"><strong>' + value + unit + '</strong></span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'scorePoints',
                    title: '百分制分数',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="text-info"><strong>' + value + '分</strong></span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'gradeScore',
                    title: '等级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 'excellent') {
                            return '<span class="badge badge-success">优秀</span>';
                        } else if (value == 'good') {
                            return '<span class="badge badge-info">良好</span>';
                        } else if (value == 'pass') {
                            return '<span class="badge badge-warning">及格</span>';
                        } else if (value == 'fail') {
                            return '<span class="badge badge-danger">不及格</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'examYear',
                    title: '考试年份',
                    align: 'center'
                },
                {
                    field: 'examSemester',
                    title: '考试学期',
                    align: 'center'
                },
                {
                    field: 'examDate',
                    title: '考试日期',
                    align: 'center'
                },
                {
                    field: 'isManual',
                    title: '录入方式',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-primary">人工录入</span>';
                        } else if (value == '0') {
                            return '<span class="badge badge-info">自动导入</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-success">有效</span>';
                        } else {
                            return '<span class="badge badge-secondary">无效</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.scoreId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.scoreId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>