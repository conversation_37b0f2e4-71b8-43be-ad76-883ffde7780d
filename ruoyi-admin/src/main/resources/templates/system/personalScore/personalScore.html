<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('我的成绩')" />
    <style>
        .score-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .score-item {
            text-align: center;
            padding: 10px;
        }
        .score-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .score-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 项目成绩卡片样式 */
        .project-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .project-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: bold;
        }
        .project-body {
            padding: 20px;
        }
        .score-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .raw-score {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .score-unit {
            font-size: 14px;
            color: #666;
            margin-left: 5px;
        }
        .score-points {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
        }
        .grade-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .grade-excellent { background: #4CAF50; color: white; }
        .grade-good { background: #2196F3; color: white; }
        .grade-pass { background: #FF9800; color: white; }
        .grade-fail { background: #F44336; color: white; }

        .score-details {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 16px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 成绩统计卡片 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="score-card">
                    <div class="row">
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="totalCount">0</div>
                            <div class="score-label">总测试项目</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="excellentCount">0</div>
                            <div class="score-label">优秀</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="goodCount">0</div>
                            <div class="score-label">良好</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="passCount">0</div>
                            <div class="score-label">及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="failCount">0</div>
                            <div class="score-label">不及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="avgScore">0</div>
                            <div class="score-label">平均分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学期选择 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="form-inline" style="margin-bottom: 20px;">
                    <label style="margin-right: 10px;">选择学期：</label>
                    <select id="semesterSelect" class="form-control" style="margin-right: 15px;">
                        <option value="2025-1">2025年第一学期</option>
                        <option value="2025-2">2025年第二学期</option>
                        <option value="2024-1">2024年第一学期</option>
                        <option value="2024-2">2024年第二学期</option>
                    </select>
                    <button class="btn btn-primary" onclick="loadScoresBysemester()">
                        <i class="fa fa-search"></i> 查看成绩
                    </button>
                    <button class="btn btn-warning" onclick="exportScores()" style="margin-left: 10px;">
                        <i class="fa fa-download"></i> 导出成绩
                    </button>
                    <button class="btn btn-info" onclick="refreshStatistics()" style="margin-left: 10px;">
                        <i class="fa fa-refresh"></i> 刷新统计
                    </button>
                </div>
            </div>
        </div>

        <!-- 成绩展示卡片 -->
        <div class="row" id="scoresContainer">
            <!-- 成绩卡片将通过JavaScript动态生成 -->
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/personalScore";
        var currentUserId = [[${currentUser.userId}]];
        var currentUserName = [[${currentUser.userName}]];
        var currentScores = []; // 存储当前成绩数据

        $(function() {
            // 初始化加载数据
            loadStatistics();
            loadScoresBysemester();
        });

        // 按学期加载成绩
        function loadScoresBysemester() {
            var semester = $("#semesterSelect").val();
            $("#scoresContainer").html('<div class="loading"><i class="fa fa-spinner fa-spin"></i> 正在加载成绩数据...</div>');

            $.post(prefix + "/list", {
                examSemester: semester
            }, function(result) {
                if (result.code == 0) {
                    currentScores = result.rows || [];
                    renderScoreCards(currentScores);
                } else {
                    $("#scoresContainer").html('<div class="no-data"><i class="fa fa-exclamation-circle"></i> 加载失败：' + result.msg + '</div>');
                }
            });
        }

        // 渲染成绩卡片
        function renderScoreCards(scores) {
            if (!scores || scores.length == 0) {
                $("#scoresContainer").html('<div class="no-data"><i class="fa fa-info-circle"></i> 该学期暂无成绩数据</div>');
                return;
            }

            var html = '';
            var itemMap = {}; // 按项目分组

            // 按项目分组
            scores.forEach(function(score) {
                if (!itemMap[score.itemId]) {
                    itemMap[score.itemId] = {
                        itemName: score.itemName,
                        itemUnit: score.itemUnit,
                        scores: []
                    };
                }
                itemMap[score.itemId].scores.push(score);
            });

            // 生成卡片HTML
            Object.keys(itemMap).forEach(function(itemId) {
                var item = itemMap[itemId];
                var latestScore = item.scores[item.scores.length - 1]; // 取最新成绩

                html += '<div class="col-md-4 col-sm-6">';
                html += '  <div class="project-card">';
                html += '    <div class="project-header">';
                html += '      <i class="fa fa-trophy"></i> ' + item.itemName;
                html += '    </div>';
                html += '    <div class="project-body">';
                html += '      <div class="score-display">';
                html += '        <div>';
                html += '          <span class="raw-score">' + (latestScore.rawScore || '-') + '</span>';
                html += '          <span class="score-unit">' + (item.itemUnit || '') + '</span>';
                html += '        </div>';
                html += '        <div class="score-points">' + (latestScore.scorePoints || '-') + '分</div>';
                html += '      </div>';
                html += '      <div style="text-align: center; margin-bottom: 15px;">';
                html += '        <span class="grade-badge grade-' + getGradeClass(latestScore.gradeScore) + '">';
                html += '          ' + getGradeName(latestScore.gradeScore);
                html += '        </span>';
                html += '      </div>';
                html += '      <div class="score-details">';
                html += '        <span>考试日期: ' + (latestScore.examDate || '-') + '</span>';
                html += '        <span>' + (latestScore.isManual == '1' ? '人工录入' : '自动导入') + '</span>';
                html += '      </div>';
                if (latestScore.remark) {
                    html += '      <div style="margin-top: 10px; font-size: 12px; color: #999;">';
                    html += '        <i class="fa fa-comment-o"></i> ' + latestScore.remark;
                    html += '      </div>';
                }
                html += '    </div>';
                html += '  </div>';
                html += '</div>';
            });

            $("#scoresContainer").html(html);
        }

        // 获取等级样式类
        function getGradeClass(grade) {
            switch(grade) {
                case 'excellent': return 'excellent';
                case 'good': return 'good';
                case 'pass': return 'pass';
                case 'fail': return 'fail';
                default: return 'pass';
            }
        }

        // 获取等级名称
        function getGradeName(grade) {
            switch(grade) {
                case 'excellent': return '优秀';
                case 'good': return '良好';
                case 'pass': return '及格';
                case 'fail': return '不及格';
                default: return '未知';
            }
        }

        // 加载统计数据
        function loadStatistics() {
            $.post(prefix + "/statistics", {}, function(result) {
                if (result.code == 0) {
                    $("#totalCount").text(result.data.totalCount || 0);
                    $("#excellentCount").text(result.data.excellentCount || 0);
                    $("#goodCount").text(result.data.goodCount || 0);
                    $("#passCount").text(result.data.passCount || 0);
                    $("#failCount").text(result.data.failCount || 0);
                    $("#avgScore").text(result.data.avgScore || 0);
                }
            });
        }

        // 刷新统计数据
        function refreshStatistics() {
            loadStatistics();
            loadScoresBysemester();
            $.modal.msg("数据已刷新");
        }

        // 导出成绩
        function exportScores() {
            var semester = $("#semesterSelect").val();
            window.location.href = prefix + "/export?examSemester=" + semester;
        }
    </script>
</body>
</html>
