<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('我的成绩')" />
    <style>
        .score-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .score-item {
            text-align: center;
            padding: 10px;
        }
        .score-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .score-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .grade-excellent { color: #28a745; font-weight: bold; }
        .grade-good { color: #17a2b8; font-weight: bold; }
        .grade-pass { color: #ffc107; font-weight: bold; }
        .grade-fail { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 成绩统计卡片 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="score-card">
                    <div class="row">
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="totalCount">0</div>
                            <div class="score-label">总测试项目</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="excellentCount">0</div>
                            <div class="score-label">优秀</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="goodCount">0</div>
                            <div class="score-label">良好</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="passCount">0</div>
                            <div class="score-label">及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="failCount">0</div>
                            <div class="score-label">不及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="avgScore">0</div>
                            <div class="score-label">平均分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索条件 -->
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>考核项目：</label>
                                <select name="itemName" class="form-control">
                                    <option value="">全部项目</option>
                                    <option value="身高体重">身高体重</option>
                                    <option value="肺活量">肺活量</option>
                                    <option value="50米跑">50米跑</option>
                                    <option value="立定跳远">立定跳远</option>
                                    <option value="坐位体前屈">坐位体前屈</option>
                                    <option value="仰卧起坐">仰卧起坐</option>
                                    <option value="引体向上">引体向上</option>
                                    <option value="800米跑">800米跑</option>
                                    <option value="1000米跑">1000米跑</option>
                                </select>
                            </li>
                            <li>
                                <label>考试学期：</label>
                                <select name="examSemester" class="form-control">
                                    <option value="">全部学期</option>
                                    <option value="2025-1">2025年第一学期</option>
                                    <option value="2025-2">2025年第二学期</option>
                                    <option value="2024-1">2024年第一学期</option>
                                    <option value="2024-2">2024年第二学期</option>
                                </select>
                            </li>
                            <li>
                                <label>成绩等级：</label>
                                <select name="gradeScore" class="form-control">
                                    <option value="">全部等级</option>
                                    <option value="excellent">优秀</option>
                                    <option value="good">良好</option>
                                    <option value="pass">及格</option>
                                    <option value="fail">不及格</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:personalScore:export">
                    <i class="fa fa-download"></i> 导出成绩
                </a>
                <a class="btn btn-info" onclick="refreshStatistics()">
                    <i class="fa fa-refresh"></i> 刷新统计
                </a>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/personalScore";
        var currentUserId = [[${currentUser.userId}]];
        var currentUserName = [[${currentUser.userName}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "个人成绩",
                showSearch: false,
                showRefresh: true,
                showToggle: true,
                showColumns: true,
                columns: [{
                    field: 'itemName',
                    title: '考核项目',
                    align: 'center',
                    cellStyle: function(value, row, index) {
                        return {
                            css: {
                                'font-weight': 'bold',
                                'font-size': '14px'
                            }
                        };
                    }
                },
                {
                    field: 'rawScore',
                    title: '原始成绩',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            var unit = row.itemUnit ? row.itemUnit : '';
                            return '<span class="text-primary"><strong>' + value + unit + '</strong></span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'scorePoints',
                    title: '百分制分数',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="text-info"><strong>' + value + '分</strong></span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'gradeScore',
                    title: '等级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 'excellent') {
                            return '<span class="badge badge-success grade-excellent">优秀</span>';
                        } else if (value == 'good') {
                            return '<span class="badge badge-info grade-good">良好</span>';
                        } else if (value == 'pass') {
                            return '<span class="badge badge-warning grade-pass">及格</span>';
                        } else if (value == 'fail') {
                            return '<span class="badge badge-danger grade-fail">不及格</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'examDate',
                    title: '考试日期',
                    align: 'center'
                },
                {
                    field: 'examSemester',
                    title: '考试学期',
                    align: 'center'
                },
                {
                    field: 'examYear',
                    title: '考试年份',
                    align: 'center'
                },
                {
                    field: 'isManual',
                    title: '录入方式',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="badge badge-primary">人工录入</span>';
                        } else if (value == '0') {
                            return '<span class="badge badge-info">自动导入</span>';
                        }
                        return '<span class="text-muted">-</span>';
                    }
                },
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '-';
                    }
                }]
            };
            $.table.init(options);
            
            // 加载统计数据
            loadStatistics();
        });

        // 加载统计数据
        function loadStatistics() {
            $.post(prefix + "/statistics", {}, function(result) {
                if (result.code == 0) {
                    $("#totalCount").text(result.data.totalCount || 0);
                    $("#excellentCount").text(result.data.excellentCount || 0);
                    $("#goodCount").text(result.data.goodCount || 0);
                    $("#passCount").text(result.data.passCount || 0);
                    $("#failCount").text(result.data.failCount || 0);
                    $("#avgScore").text(result.data.avgScore || 0);
                }
            });
        }

        // 刷新统计数据
        function refreshStatistics() {
            loadStatistics();
            $.modal.msg("统计数据已刷新");
        }
    </script>
</body>
</html>
