<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('我的成绩')" />
    <style>
        .score-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .score-item {
            text-align: center;
            padding: 10px;
        }
        .score-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .score-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 项目成绩卡片样式 */
        .score-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .project-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            height: 200px;
            display: flex;
            flex-direction: column;
        }

        .project-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        }

        .project-card.no-data {
            opacity: 0.6;
            background: #f8f9fa;
        }

        .project-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            flex-shrink: 0;
        }

        .project-body {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .score-main {
            margin-bottom: 12px;
        }

        .raw-score {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .score-unit {
            font-size: 12px;
            color: #666;
            margin-left: 4px;
        }

        .score-points {
            font-size: 16px;
            font-weight: bold;
            color: #2196F3;
            margin: 8px 0;
        }

        .grade-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .grade-excellent { background: #4CAF50; color: white; }
        .grade-good { background: #2196F3; color: white; }
        .grade-pass { background: #FF9800; color: white; }
        .grade-fail { background: #F44336; color: white; }
        .grade-none { background: #E0E0E0; color: #666; }

        .no-score {
            color: #999;
            font-size: 14px;
            font-style: italic;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
            font-size: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .score-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .score-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 成绩统计卡片 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="score-card">
                    <div class="row">
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="totalCount">0</div>
                            <div class="score-label">总测试项目</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="excellentCount">0</div>
                            <div class="score-label">优秀</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="goodCount">0</div>
                            <div class="score-label">良好</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="passCount">0</div>
                            <div class="score-label">及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="failCount">0</div>
                            <div class="score-label">不及格</div>
                        </div>
                        <div class="col-sm-2 score-item">
                            <div class="score-number" id="avgScore">0</div>
                            <div class="score-label">平均分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学期选择 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="form-inline" style="margin-bottom: 20px;">
                    <label style="margin-right: 10px;">选择学期：</label>
                    <select id="semesterSelect" class="form-control" style="margin-right: 15px;">
                        <option value="2025-1">2025年第一学期</option>
                        <option value="2025-2">2025年第二学期</option>
                        <option value="2024-1">2024年第一学期</option>
                        <option value="2024-2">2024年第二学期</option>
                    </select>
                    <button class="btn btn-primary" onclick="loadScoresBysemester()">
                        <i class="fa fa-search"></i> 查看成绩
                    </button>
                    <button class="btn btn-warning" onclick="exportScores()" style="margin-left: 10px;">
                        <i class="fa fa-download"></i> 导出成绩
                    </button>
                    <button class="btn btn-info" onclick="refreshStatistics()" style="margin-left: 10px;">
                        <i class="fa fa-refresh"></i> 刷新统计
                    </button>
                </div>
            </div>
        </div>

        <!-- 成绩展示网格 -->
        <div class="score-grid" id="scoresContainer">
            <!-- 成绩卡片将通过JavaScript动态生成 -->
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/personalScore";
        var currentUserId = [[${currentUser.userId}]];
        var currentUserName = [[${currentUser.userName}]];
        var currentScores = []; // 存储当前成绩数据

        $(function() {
            // 初始化加载数据
            loadStatistics();
            loadScoresBysemester();
        });

        // 按学期加载成绩
        function loadScoresBysemester() {
            var semester = $("#semesterSelect").val();
            $("#scoresContainer").html('<div class="loading"><i class="fa fa-spinner fa-spin"></i> 正在加载成绩数据...</div>');

            // 先尝试使用原来的list接口
            $.post(prefix + "/list", {
                examSemester: semester
            }, function(result) {
                console.log("成绩数据返回:", result); // 调试日志
                if (result.code == 0) {
                    var scores = result.rows || [];
                    console.log("成绩数组:", scores); // 调试日志
                    renderScoreCardsFromList(scores);
                } else {
                    $("#scoresContainer").html('<div class="loading">加载失败：' + result.msg + '</div>');
                }
            }).fail(function(xhr, status, error) {
                console.error("请求失败:", error);
                $("#scoresContainer").html('<div class="loading">请求失败，请检查网络连接</div>');
            });
        }

        // 从原始成绩列表渲染固定布局的成绩卡片
        function renderScoreCardsFromList(scores) {
            console.log("开始从列表渲染成绩卡片，数据:", scores); // 调试日志

            // 定义9个固定项目
            var projects = [
                {itemId: 1, itemName: "身高体重", itemUnit: "BMI"},
                {itemId: 2, itemName: "肺活量", itemUnit: "ml"},
                {itemId: 3, itemName: "50米跑", itemUnit: "秒"},
                {itemId: 4, itemName: "立定跳远", itemUnit: "cm"},
                {itemId: 5, itemName: "坐位体前屈", itemUnit: "cm"},
                {itemId: 6, itemName: "仰卧起坐", itemUnit: "个/分钟"},
                {itemId: 7, itemName: "引体向上", itemUnit: "个"},
                {itemId: 8, itemName: "800米跑", itemUnit: "分:秒"},
                {itemId: 9, itemName: "1000米跑", itemUnit: "分:秒"}
            ];

            var html = '';

            // 按固定顺序渲染9个项目卡片
            projects.forEach(function(project) {
                // 查找对应项目的成绩
                var score = scores.find(function(s) {
                    return s.itemId == project.itemId;
                });

                var hasData = score != null;
                var cardClass = hasData ? 'project-card' : 'project-card no-data';

                html += '<div class="' + cardClass + '">';
                html += '  <div class="project-header">';
                html += '    <i class="fa fa-trophy"></i> ' + project.itemName;
                html += '  </div>';
                html += '  <div class="project-body">';

                if (hasData) {
                    // 有成绩数据
                    html += '    <div class="score-main">';
                    html += '      <div class="raw-score">' + (score.rawScore || '-');
                    html += '        <span class="score-unit">' + project.itemUnit + '</span>';
                    html += '      </div>';
                    html += '    </div>';
                    html += '    <div class="score-points">' + (score.scorePoints || '-') + '分</div>';
                    html += '    <div>';
                    html += '      <span class="grade-badge grade-' + getGradeClass(score.gradeScore) + '">';
                    html += '        ' + getGradeName(score.gradeScore);
                    html += '      </span>';
                    html += '    </div>';
                } else {
                    // 无成绩数据
                    html += '    <div class="no-score">';
                    html += '      <div style="font-size: 24px; margin-bottom: 10px;"><i class="fa fa-minus-circle"></i></div>';
                    html += '      <div>暂无成绩</div>';
                    html += '      <div class="grade-badge grade-none">未测试</div>';
                    html += '    </div>';
                }

                html += '  </div>';
                html += '</div>';
            });

            $("#scoresContainer").html(html);
            console.log("成绩卡片渲染完成，HTML长度:", html.length); // 调试日志
        }

        // 获取等级样式类
        function getGradeClass(grade) {
            switch(grade) {
                case 'excellent': return 'excellent';
                case 'good': return 'good';
                case 'pass': return 'pass';
                case 'fail': return 'fail';
                default: return 'none';
            }
        }

        // 获取等级名称
        function getGradeName(grade) {
            switch(grade) {
                case 'excellent': return '优秀';
                case 'good': return '良好';
                case 'pass': return '及格';
                case 'fail': return '不及格';
                default: return '未测试';
            }
        }

        // 加载统计数据
        function loadStatistics() {
            $.post(prefix + "/statistics", {}, function(result) {
                if (result.code == 0) {
                    $("#totalCount").text(result.data.totalCount || 0);
                    $("#excellentCount").text(result.data.excellentCount || 0);
                    $("#goodCount").text(result.data.goodCount || 0);
                    $("#passCount").text(result.data.passCount || 0);
                    $("#failCount").text(result.data.failCount || 0);
                    $("#avgScore").text(result.data.avgScore || 0);
                }
            });
        }

        // 刷新统计数据
        function refreshStatistics() {
            loadStatistics();
            loadScoresBysemester();
            $.modal.msg("数据已刷新");
        }

        // 导出成绩
        function exportScores() {
            var semester = $("#semesterSelect").val();
            window.location.href = prefix + "/export?examSemester=" + semester;
        }
    </script>
</body>
</html>
