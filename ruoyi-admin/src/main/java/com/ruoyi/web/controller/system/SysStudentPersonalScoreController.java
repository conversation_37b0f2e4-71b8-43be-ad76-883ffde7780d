package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysStudentScore;
import com.ruoyi.system.service.ISysStudentScoreService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 学生个人成绩Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/system/personalScore")
public class SysStudentPersonalScoreController extends BaseController
{
    private String prefix = "system/personalScore";

    @Autowired
    private ISysStudentScoreService sysStudentScoreService;

    @RequiresPermissions("system:personalScore:view")
    @GetMapping()
    public String personalScore(ModelMap mmap)
    {
        // 获取当前登录用户信息
        SysUser currentUser = ShiroUtils.getSysUser();
        mmap.put("currentUser", currentUser);
        return prefix + "/personalScore";
    }

    /**
     * 查询个人成绩列表
     */
    @RequiresPermissions("system:personalScore:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysStudentScore sysStudentScore)
    {
        // 获取当前登录用户ID
        SysUser currentUser = ShiroUtils.getSysUser();
        Long currentUserId = currentUser.getUserId();
        
        // 设置查询条件为当前用户ID
        sysStudentScore.setStudentId(currentUserId);
        
        startPage();
        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(sysStudentScore);
        return getDataTable(list);
    }

    /**
     * 导出个人成绩列表
     */
    @RequiresPermissions("system:personalScore:export")
    @Log(title = "个人成绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysStudentScore sysStudentScore)
    {
        // 获取当前登录用户ID
        SysUser currentUser = ShiroUtils.getSysUser();
        Long currentUserId = currentUser.getUserId();
        
        // 设置查询条件为当前用户ID
        sysStudentScore.setStudentId(currentUserId);
        
        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(sysStudentScore);
        ExcelUtil<SysStudentScore> util = new ExcelUtil<SysStudentScore>(SysStudentScore.class);
        return util.exportExcel(list, "个人成绩数据");
    }

    /**
     * 获取成绩统计信息
     */
    @RequiresPermissions("system:personalScore:view")
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult getStatistics()
    {
        // 获取当前登录用户ID
        SysUser currentUser = ShiroUtils.getSysUser();
        Long currentUserId = currentUser.getUserId();

        // 查询该学生的所有成绩
        SysStudentScore queryParam = new SysStudentScore();
        queryParam.setStudentId(currentUserId);
        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(queryParam);

        // 统计各等级成绩数量
        long excellentCount = list.stream().filter(s -> "excellent".equals(s.getGradeScore())).count();
        long goodCount = list.stream().filter(s -> "good".equals(s.getGradeScore())).count();
        long passCount = list.stream().filter(s -> "pass".equals(s.getGradeScore())).count();
        long failCount = list.stream().filter(s -> "fail".equals(s.getGradeScore())).count();

        // 计算平均分
        double avgScore = list.stream()
            .filter(s -> s.getScorePoints() != null)
            .mapToDouble(s -> s.getScorePoints().doubleValue())
            .average()
            .orElse(0.0);

        AjaxResult result = AjaxResult.success();
        result.put("totalCount", list.size());
        result.put("excellentCount", excellentCount);
        result.put("goodCount", goodCount);
        result.put("passCount", passCount);
        result.put("failCount", failCount);
        result.put("avgScore", Math.round(avgScore * 100.0) / 100.0);

        return result;
    }

    /**
     * 获取成绩卡片数据
     */
    @RequiresPermissions("system:personalScore:view")
    @PostMapping("/scoreCards")
    @ResponseBody
    public AjaxResult getScoreCards(String examSemester)
    {
        // 获取当前登录用户ID
        SysUser currentUser = ShiroUtils.getSysUser();
        Long currentUserId = currentUser.getUserId();

        // 查询该学生指定学期的成绩
        SysStudentScore queryParam = new SysStudentScore();
        queryParam.setStudentId(currentUserId);
        if (examSemester != null && !examSemester.isEmpty()) {
            queryParam.setExamSemester(examSemester);
        }

        List<SysStudentScore> list = sysStudentScoreService.selectSysStudentScoreList(queryParam);

        // 定义9个固定项目的顺序和信息
        String[][] projectInfo = {
            {"1", "身高体重", "BMI"},
            {"2", "肺活量", "ml"},
            {"3", "50米跑", "秒"},
            {"4", "立定跳远", "cm"},
            {"5", "坐位体前屈", "cm"},
            {"6", "仰卧起坐", "个/分钟"},
            {"7", "引体向上", "个"},
            {"8", "800米跑", "分:秒"},
            {"9", "1000米跑", "分:秒"}
        };

        // 构建固定顺序的成绩卡片数据
        java.util.List<java.util.Map<String, Object>> scoreCards = new java.util.ArrayList<>();

        for (String[] project : projectInfo) {
            java.util.Map<String, Object> card = new java.util.HashMap<>();
            card.put("itemId", project[0]);
            card.put("itemName", project[1]);
            card.put("itemUnit", project[2]);

            // 查找对应项目的成绩
            SysStudentScore score = list.stream()
                .filter(s -> project[0].equals(String.valueOf(s.getItemId())))
                .findFirst()
                .orElse(null);

            if (score != null) {
                card.put("rawScore", score.getRawScore());
                card.put("scorePoints", score.getScorePoints());
                card.put("gradeScore", score.getGradeScore());
                card.put("examDate", score.getExamDate());
                card.put("isManual", score.getIsManual());
                card.put("remark", score.getRemark());
                card.put("hasData", true);
            } else {
                card.put("rawScore", null);
                card.put("scorePoints", null);
                card.put("gradeScore", null);
                card.put("examDate", null);
                card.put("isManual", null);
                card.put("remark", "暂无成绩");
                card.put("hasData", false);
            }

            scoreCards.add(card);
        }

        return AjaxResult.success(scoreCards);
    }
}
